'use client'

import { useEffect, useState } from 'react'

export type DomainType = 'app' | 'mapped'

export interface DomainInfo {
  type: DomainType
  hostname: string
  isAppDomain: boolean
  isMappedDomain: boolean
}

/**
 * Hook to detect domain type and provide domain information
 * - 'app': APP_URL domain (supports OAuth + email/password for User login)
 * - 'mapped': Custom mapped domain (only email/password for Customer login)
 */
export function useDomainType(): DomainInfo {
  const [domainInfo, setDomainInfo] = useState<DomainInfo>({
    type: 'app',
    hostname: '',
    isAppDomain: true,
    isMappedDomain: false,
  })

  useEffect(() => {
    if (typeof window === 'undefined') return

    const hostname = window.location.hostname
    const appUrl = process.env.NEXT_PUBLIC_APP_URL
    const appHostname = appUrl ? new URL(appUrl).hostname : null

    const isAppDomain = hostname === appHostname
    const isMappedDomain = !isAppDomain

    setDomainInfo({
      type: isAppDomain ? 'app' : 'mapped',
      hostname,
      isAppDomain,
      isMappedDomain,
    })
  }, [])

  return domainInfo
}
