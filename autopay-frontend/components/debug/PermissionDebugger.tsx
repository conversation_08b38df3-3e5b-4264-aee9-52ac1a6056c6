'use client'

import { useClientPermissions } from '@/app/(core)/common/auth/ClientPermissionProvider'
import useAuth from '@/lib/hooks/useAuth'
import { useServerPermissions } from '@/lib/hooks/useServerPermissions'
import { useSelectedOrganization, useSelectedTeam } from '@/lib/stores/organizationStore'
import { useMemo, useState } from 'react'

export function PermissionDebugger() {
  const [isVisible, setIsVisible] = useState(false)
  const [isMinimized, setIsMinimized] = useState(true)
  const [copiedText, setCopiedText] = useState<string | null>(null)

  const { user, loading: authLoading } = useAuth()
  const selectedOrganization = useSelectedOrganization()
  const selectedTeam = useSelectedTeam()

  // Server permissions (primary)
  const {
    permissions: serverPermissions,
    isLoading: serverLoading,
    ability: serverAbility,
    user: serverUser,
  } = useServerPermissions()

  // Debug: Also get ability directly from client context
  const { ability: directAbility } = useClientPermissions()

  const context = useMemo(() => {
    // Priority: use selected organization/team from store
    if (selectedOrganization?.id) {
      return {
        organization_id: selectedOrganization.id,
        team_id: selectedTeam?.id,
      }
    }

    // Second priority: use current organization/team from user cookie
    if (user?.current_organization?.id) {
      return {
        organization_id: user.current_organization?.id,
        team_id: user.current_team?.id,
      }
    }

    return null
  }, [selectedOrganization?.id, selectedTeam?.id, user?.current_organization?.id, user?.current_team?.id])

  // Toggle visibility
  const toggleVisibility = () => {
    if (!isVisible) {
      setIsVisible(true)
      setIsMinimized(false)
    } else {
      setIsVisible(false)
      setIsMinimized(true)
    }
  }

  // Toggle minimize/maximize when visible
  const toggleMinimize = () => {
    setIsMinimized(!isMinimized)
  }

  // Copy text to clipboard
  const copyToClipboard = async (text: string, label: string) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopiedText(label)
      setTimeout(() => setCopiedText(null), 2000) // Clear after 2 seconds
    } catch (err) {
      console.error('Failed to copy text: ', err)
    }
  }
  // Only show in a development environment
  if (process.env.NODE_ENV !== 'development') {
    return null
  }

  return (
    <>
      {/* Toggle Button */}
      {!isVisible && (
        <button
          onClick={toggleVisibility}
          className="fixed bottom-4 left-4 z-50 rounded-full bg-blue-600 p-3 text-white shadow-lg transition-colors hover:bg-blue-700"
          title="Show Permission Debugger">
          <svg
            className="h-5 w-5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
        </button>
      )}

      {/* Debug Panel */}
      {isVisible && (
        <div
          className={`bg-opacity-95 fixed bottom-4 left-4 z-50 rounded-lg border border-gray-600 bg-black text-xs text-white shadow-xl transition-all duration-200 ${
            isMinimized ? 'w-64' : 'w-fit max-w-md'
          }`}>
          {/* Header */}
          <div className="flex items-center justify-between border-b border-gray-600 p-3">
            <div className="text-sm font-bold">Permission Debugger</div>
            <div className="flex items-center space-x-1">
              <button
                onClick={toggleMinimize}
                className="rounded p-1 transition-colors hover:bg-gray-700"
                title={isMinimized ? 'Expand' : 'Minimize'}>
                <svg
                  className="h-4 w-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24">
                  {isMinimized ? (
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4"
                    />
                  ) : (
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M20 12H4"
                    />
                  )}
                </svg>
              </button>
              <button
                onClick={toggleVisibility}
                className="rounded p-1 transition-colors hover:bg-gray-700"
                title="Close">
                <svg
                  className="h-4 w-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>
          </div>

          {/* Content */}
          {!isMinimized && (
            <div className="max-h-96 space-y-2 overflow-y-auto p-3">
              <div className="grid grid-cols-3 gap-2 text-xs">
                <div>
                  <span className="text-gray-400">User:</span>
                  <div
                    className="relative cursor-pointer truncate rounded px-1 font-mono text-green-400 transition-colors hover:bg-gray-700"
                    onClick={() => user?.id && copyToClipboard(user.id, 'User ID')}
                    title={user?.id ? `Click to copy: ${user.id}` : 'No user ID'}>
                    {user?.id || 'None'}
                    {copiedText === 'User ID' && (
                      <span className="absolute -top-6 left-0 rounded bg-green-600 px-2 py-1 text-xs text-white">
                        Copied!
                      </span>
                    )}
                  </div>
                </div>

                <div>
                  <span className="text-gray-400">Auth Loading:</span>
                  <div className={`font-semibold ${authLoading ? 'text-yellow-400' : 'text-green-400'}`}>
                    {authLoading ? 'Loading' : 'Ready'}
                  </div>
                </div>

                <div>
                  <span className="text-gray-400">Server Loading:</span>
                  <div className={`font-semibold ${serverLoading ? 'text-yellow-400' : 'text-green-400'}`}>
                    {serverLoading ? 'Loading' : 'Ready'}
                  </div>
                </div>

                <div>
                  <span className="text-gray-400">Organization:</span>
                  <div
                    className="relative cursor-pointer truncate rounded px-1 font-mono text-blue-400 transition-colors hover:bg-gray-700"
                    onClick={() =>
                      selectedOrganization?.id && copyToClipboard(selectedOrganization.id, 'Organization ID')
                    }
                    title={
                      selectedOrganization?.id ? `Click to copy ID: ${selectedOrganization.id}` : 'No organization ID'
                    }>
                    {selectedOrganization?.name || 'None'}
                    {copiedText === 'Organization ID' && (
                      <span className="absolute -top-6 left-0 rounded bg-green-600 px-2 py-1 text-xs text-white">
                        Copied!
                      </span>
                    )}
                  </div>
                </div>

                <div>
                  <span className="text-gray-400">Server Permissions:</span>
                  <div className="font-semibold text-cyan-400">{serverPermissions?.length || 0}</div>
                </div>

                <div>
                  <span className="text-gray-400">Is Owner (Server):</span>
                  <div
                    className={`font-semibold ${serverUser?.current_organization.is_owner ? 'text-green-400' : 'text-red-400'}`}>
                    {serverUser?.current_organization.is_owner ? 'Yes' : 'No'}
                  </div>
                </div>

                <div>
                  <span className="text-gray-400">User Current Org:</span>
                  <div
                    className="relative cursor-pointer truncate rounded px-1 font-mono text-orange-400 transition-colors hover:bg-gray-700"
                    onClick={() =>
                      user?.current_organization?.id &&
                      copyToClipboard(user.current_organization?.id, 'User Current Org ID')
                    }
                    title={
                      user?.current_organization?.id
                        ? `Click to copy: ${user.current_organization?.id}`
                        : 'No current org ID'
                    }>
                    {user?.current_organization?.id || 'None'}
                    {copiedText === 'User Current Org ID' && (
                      <span className="absolute -top-6 left-0 rounded bg-green-600 px-2 py-1 text-xs text-white">
                        Copied!
                      </span>
                    )}
                  </div>
                </div>

                <div>
                  <span className="text-gray-400">User Current Team:</span>
                  <div
                    className="relative cursor-pointer truncate rounded px-1 font-mono text-orange-400 transition-colors hover:bg-gray-700"
                    onClick={() =>
                      user?.current_team?.id && copyToClipboard(user.current_team?.id, 'User Current Team ID')
                    }
                    title={user?.current_team?.id ? `Click to copy: ${user.current_team?.id}` : 'No current team ID'}>
                    {user?.current_team?.id || 'None'}
                    {copiedText === 'User Current Team ID' && (
                      <span className="absolute -top-6 left-0 rounded bg-green-600 px-2 py-1 text-xs text-white">
                        Copied!
                      </span>
                    )}
                  </div>
                </div>

                <div>
                  <span className="text-gray-400">Context Source:</span>
                  <div className="font-semibold text-yellow-400">
                    {selectedOrganization?.id ? 'Store' : user?.current_organization?.id ? 'Cookie' : 'None'}
                  </div>
                </div>

                <div>
                  <span className="text-gray-400">Team:</span>
                  <div
                    className="relative cursor-pointer truncate rounded px-1 font-mono text-purple-400 transition-colors hover:bg-gray-700"
                    onClick={() => selectedTeam?.id && copyToClipboard(selectedTeam.id, 'Team ID')}
                    title={selectedTeam?.id ? `Click to copy ID: ${selectedTeam.id}` : 'No team ID'}>
                    {selectedTeam?.name || 'None'}
                    {copiedText === 'Team ID' && (
                      <span className="absolute -top-6 left-0 rounded bg-green-600 px-2 py-1 text-xs text-white">
                        Copied!
                      </span>
                    )}
                  </div>
                </div>

                <div>
                  <span className="text-gray-400">Server Ability Rules:</span>
                  <div className="font-semibold text-cyan-400">{serverAbility?.rules?.length || 0}</div>
                </div>

                <div>
                  <span className="text-gray-400">Is Owner (User Cookie):</span>
                  <div
                    className={`font-semibold ${user?.current_organization?.is_owner ? 'text-green-400' : 'text-red-400'}`}>
                    {user?.current_organization?.is_owner ? 'Yes' : 'No'}
                  </div>
                </div>

                <div>
                  <span className="text-gray-400">Can Manage All (Server):</span>
                  <div
                    className={`font-semibold ${serverAbility?.can('manage', 'all' as any) ? 'text-green-400' : 'text-red-400'}`}>
                    {serverAbility?.can('manage', 'all' as any) ? 'Yes' : 'No'}
                  </div>
                </div>

                <div>
                  <span className="text-gray-400">Can Manage All (Direct):</span>
                  <div
                    className={`font-semibold ${directAbility?.can('manage', 'all' as any) ? 'text-green-400' : 'text-red-400'}`}>
                    {directAbility?.can('manage', 'all' as any) ? 'Yes' : 'No'}
                  </div>
                </div>

                <div>
                  <span className="text-gray-400">Server Permissions Count:</span>
                  <div className="font-semibold text-cyan-400">{serverPermissions?.length || 0}</div>
                </div>

                <div>
                  <span className="text-gray-400">Has Admin Rule:</span>
                  <div
                    className={`font-semibold ${
                      serverAbility?.rules?.some((rule: any) => rule.action === 'manage' && rule.subject === 'all')
                        ? 'text-green-400'
                        : 'text-red-400'
                    }`}>
                    {serverAbility?.rules?.some((rule: any) => rule.action === 'manage' && rule.subject === 'all')
                      ? 'Yes'
                      : 'No'}
                  </div>
                </div>
              </div>

              {serverPermissions && serverPermissions.length > 0 && (
                <details className="mt-2">
                  <summary className="cursor-pointer text-sm font-semibold text-gray-300 transition-colors hover:text-white">
                    🔐 Server Permissions ({serverPermissions.length})
                  </summary>
                  <div className="mt-2 max-h-32 overflow-y-auto rounded bg-gray-800 p-2">
                    {serverPermissions.slice(0, 20).map((permission: string, index: number) => (
                      <div
                        key={index}
                        className="border-b border-gray-700 py-0.5 font-mono text-xs text-blue-300 last:border-b-0">
                        {permission}
                      </div>
                    ))}
                    {serverPermissions.length > 20 && (
                      <div className="mt-1 text-xs text-gray-500 italic">
                        ... and {serverPermissions.length - 20} more permissions
                      </div>
                    )}
                  </div>
                </details>
              )}

              {serverAbility?.rules && serverAbility.rules.length > 0 && (
                <details className="mt-2">
                  <summary className="cursor-pointer text-sm font-semibold text-gray-300 transition-colors hover:text-white">
                    ⚙️ CASL Ability Rules ({serverAbility.rules.length})
                  </summary>
                  <div className="mt-2 max-h-32 overflow-y-auto rounded bg-gray-800 p-2">
                    {serverAbility.rules.slice(0, 10).map((rule: any, index: number) => (
                      <div
                        key={index}
                        className="border-b border-gray-700 py-1 text-xs text-yellow-300 last:border-b-0">
                        <div className="font-mono">
                          {rule.action} → {rule.subject}
                          {rule.conditions && Object.keys(rule.conditions).length > 0 && (
                            <span className="text-gray-400"> (with conditions)</span>
                          )}
                        </div>
                      </div>
                    ))}
                    {serverAbility.rules.length > 10 && (
                      <div className="mt-1 text-xs text-gray-500 italic">
                        ... and {serverAbility.rules.length - 10} more rules
                      </div>
                    )}
                  </div>
                </details>
              )}

              <div className="mt-3 border-t border-gray-600 pt-3">
                <div className="mb-2 text-sm font-semibold text-gray-300">🔍 Debug Info:</div>
                <div className="space-y-1 text-xs">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-400">User Cookie is_owner:</span>
                    <span
                      className={`font-mono ${user?.current_organization?.is_owner ? 'text-green-400' : 'text-red-400'}`}>
                      {user?.current_organization?.is_owner ? 'true' : 'false'}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-400">Server User is_owner:</span>
                    <span
                      className={`font-mono ${serverUser?.current_organization?.is_owner ? 'text-green-400' : 'text-red-400'}`}>
                      {serverUser?.current_organization?.is_owner ? 'true' : 'false'}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-400">Permissions Array Empty:</span>
                    <span
                      className={`font-mono ${serverPermissions?.length === 0 ? 'text-green-400' : 'text-red-400'}`}>
                      {serverPermissions?.length === 0 ? 'true' : 'false'}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-400">Admin Rule Exists:</span>
                    <span
                      className={`font-mono ${
                        serverAbility?.rules?.some((rule: any) => rule.action === 'manage' && rule.subject === 'all')
                          ? 'text-green-400'
                          : 'text-red-400'
                      }`}>
                      {serverAbility?.rules?.some((rule: any) => rule.action === 'manage' && rule.subject === 'all')
                        ? 'true'
                        : 'false'}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-400">Raw serverAbility:</span>
                    <span className="font-mono text-xs break-all text-yellow-400">
                      {JSON.stringify(serverAbility?.rules || [])}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-400">Direct can() test:</span>
                    <span
                      className={`font-mono ${(() => {
                        try {
                          return serverAbility?.can('manage', 'all' as any) ? 'text-green-400' : 'text-red-400'
                        } catch (e) {
                          return 'text-red-400'
                        }
                      })()}`}>
                      {(() => {
                        try {
                          return serverAbility?.can('manage', 'all' as any) ? 'true' : 'false'
                        } catch (e) {
                          return `error: ${e}`
                        }
                      })()}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-400">Direct Ability Rules:</span>
                    <span className="font-mono text-xs break-all text-cyan-400">
                      {JSON.stringify(directAbility?.rules || [])}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-400">Direct can manage all:</span>
                    <span
                      className={`font-mono ${directAbility?.can('manage', 'all' as any) ? 'text-green-400' : 'text-red-400'}`}>
                      {directAbility?.can('manage', 'all' as any) ? 'true' : 'false'}
                    </span>
                  </div>
                </div>
              </div>

              <div className="mt-3 border-t border-gray-600 pt-3">
                <div className="mb-2 text-sm font-semibold text-gray-300">🧪 Test Server Permissions:</div>
                <div className="space-y-1">
                  <div className="flex items-center justify-between text-xs">
                    <span className="font-mono text-gray-400">server:instance:view</span>
                    <span
                      className={`rounded px-2 py-0.5 text-xs font-semibold ${
                        serverAbility?.can('view', 'server:instance' as any)
                          ? 'bg-blue-600 text-white'
                          : 'bg-red-600 text-white'
                      }`}>
                      {serverAbility?.can('view', 'server:instance' as any) ? 'ALLOWED' : 'DENIED'}
                    </span>
                  </div>
                  <div className="flex items-center justify-between text-xs">
                    <span className="font-mono text-gray-400">server:instance:create</span>
                    <span
                      className={`rounded px-2 py-0.5 text-xs font-semibold ${
                        serverAbility?.can('create', 'server:instance' as any)
                          ? 'bg-blue-600 text-white'
                          : 'bg-red-600 text-white'
                      }`}>
                      {serverAbility?.can('create', 'server:instance' as any) ? 'ALLOWED' : 'DENIED'}
                    </span>
                  </div>
                  <div className="flex items-center justify-between text-xs">
                    <span className="font-mono text-gray-400">server:database:view</span>
                    <span
                      className={`rounded px-2 py-0.5 text-xs font-semibold ${
                        serverAbility?.can('view', 'server:database' as any)
                          ? 'bg-blue-600 text-white'
                          : 'bg-red-600 text-white'
                      }`}>
                      {serverAbility?.can('view', 'server:database' as any) ? 'ALLOWED' : 'DENIED'}
                    </span>
                  </div>
                  <div className="flex items-center justify-between text-xs">
                    <span className="font-mono text-gray-400">organization:admin</span>
                    <span
                      className={`rounded px-2 py-0.5 text-xs font-semibold ${
                        serverAbility?.can('admin', 'organization' as any)
                          ? 'bg-purple-600 text-white'
                          : 'bg-red-600 text-white'
                      }`}>
                      {serverAbility?.can('admin', 'organization' as any) ? 'ALLOWED' : 'DENIED'}
                    </span>
                  </div>
                  <div className="flex items-center justify-between text-xs">
                    <span className="font-mono text-gray-400">manage:all (Owner)</span>
                    <span
                      className={`rounded px-2 py-0.5 text-xs font-semibold ${
                        serverAbility?.can('manage', 'all' as any) ? 'bg-green-600 text-white' : 'bg-red-600 text-white'
                      }`}>
                      {serverAbility?.can('manage', 'all' as any) ? 'ALLOWED' : 'DENIED'}
                    </span>
                  </div>
                </div>
              </div>

              {context && (
                <div className="mt-3 border-t border-gray-600 pt-3">
                  <div className="mb-2 text-sm font-semibold text-gray-300">🎯 Current Context:</div>
                  <div className="space-y-1 text-xs">
                    <div className="flex items-center justify-between">
                      <span className="text-gray-400">Organization ID:</span>
                      <span className="font-mono text-blue-400">{context.organization_id}</span>
                    </div>
                    {context.team_id && (
                      <div className="flex items-center justify-between">
                        <span className="text-gray-400">Team ID:</span>
                        <span className="font-mono text-purple-400">{context.team_id}</span>
                      </div>
                    )}
                    {/*{context.resource_id && (*/}
                    {/*  <div className="flex items-center justify-between">*/}
                    {/*    <span className="text-gray-400">Resource ID:</span>*/}
                    {/*    <span className="font-mono text-green-400">{context.resource_id}</span>*/}
                    {/*  </div>*/}
                    {/*)}*/}
                  </div>
                </div>
              )}

              <div className="mt-3 border-t border-gray-600 pt-3">
                <div className="mb-2 text-sm font-semibold text-gray-300">🔍 Permission Parsing Test:</div>
                <div className="space-y-1 text-xs">
                  <div className="font-mono text-gray-400">
                    &#34;server:instance:view&#34; → action: &#34;view&#34;, subject: &#34;server:instance&#34;
                  </div>
                  <div className="font-mono text-gray-400">
                    &#34;organization:admin&#34; → action: &#34;admin&#34;, subject: &#34;organization&#34;
                  </div>
                  <div className="font-mono text-gray-400">
                    &#34;manage:all&#34; → action: &#34;manage&#34;, subject: &#34;all&#34;
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </>
  )
}
