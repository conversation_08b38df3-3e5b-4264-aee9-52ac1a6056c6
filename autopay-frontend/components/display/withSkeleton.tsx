import { Skeleton } from '@/components/ui/skeleton'
import React from 'react'

interface WithSkeletonProps {
  loading: boolean
  children: React.ReactNode
  skeleton?: React.ReactNode
  className?: string
}

export function withSkeleton<T extends object>(
  Component: React.ComponentType<T>,
  skeletonProps?: {
    titleWidth?: string
    descriptionWidth?: string
    fieldHeight?: string
    buttonWidth?: string
  }
) {
  const {
    titleWidth = 'w-48',
    descriptionWidth = 'w-64',
    fieldHeight = 'h-10',
    buttonWidth = 'w-20',
  } = skeletonProps || {}

  const WrappedComponent = ({ loading, ...props }: WithSkeletonProps & T) => {
    if (loading) {
      return (
        <div className="space-y-6">
          <div className="space-y-2">
            <Skeleton className={`h-6 ${titleWidth}`} />
            <Skeleton className={`h-4 ${descriptionWidth}`} />
          </div>
          <div className="space-y-4">
            <Skeleton className={`${fieldHeight} w-full rounded-md`} />
          </div>
          <div className="flex justify-end">
            <Skeleton className={`h-9 ${buttonWidth}`} />
          </div>
        </div>
      )
    }
    return <Component {...(props as T)} />
  }

  WrappedComponent.displayName = `withSkeleton(${Component.displayName || Component.name || 'Component'})`

  return WrappedComponent
}
