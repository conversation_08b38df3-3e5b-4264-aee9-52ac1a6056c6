import { auth } from '@/auth'
import { getClientIp } from '@/lib/utils/clientIp'
import { queryFetchHelper } from '@/lib/utils/fetchHelper'
import { get, has } from 'lodash'
import { Session } from 'next-auth'
import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'
import z from 'zod'

// OAuth provider schema
const oauthProviderSchema = z.object({
  provider: z.enum(['google', 'github', 'discord', 'facebook', 'twitter']),
  state: z.string().optional(),
  code: z.string().optional(),
  providerAccountId: z.string().optional(),
  access_token: z.string().optional(),
  refresh_token: z.string().optional(),
})

// OAuth check schema
const oauthCheckSchema = z.object({
  // User information from the session
  id: z.string().optional(),
  name: z.string().optional(),
  email: z.string().email().optional(),
  image: z.string().url().optional(),

  // Request metadata
  ip: z.string().optional(),
  userAgent: z.string().optional(),

  // OAuth-specific data
  mode: z.enum(['login', 'register', 'connect', 'disconnect']),
  oauth: oauthProviderSchema,
})

export async function POST(request: NextRequest) {
  const session: Session | null = await auth()
  const cookieStore = await cookies()

  if (!session) {
    return NextResponse.json(
      {
        success: false,
        message: 'No session found',
      },
      {
        status: 401,
      }
    )
  }

  const body = await request.json()
  const ip = getClientIp(request) || 'unknown'
  const userAgent = request.headers.get('user-agent') || 'unknown'

  // Add user agent and IP to the request body
  const { user } = session

  const data = {
    ip,
    userAgent,
    ...body,
    ...user,
  }

  const validate = oauthCheckSchema.safeParse(data)

  if (!validate.success) {
    return NextResponse.json(
      {
        success: false,
        message: validate.error.errors[0].message,
        errors: validate.error.errors,
      },
      {
        status: 400,
      }
    )
  }

  // Send the request to the API server
  try {
    const response: ApiResponse = await queryFetchHelper(`/${validate.data.mode}/${validate.data.oauth.provider}`, {
      method: 'POST',
      body: JSON.stringify(validate.data),
    })

    if (response.success && has(response.data, 'token')) {
      cookieStore.set({
        name: process.env.APP_NAME + '.authorization',
        value: get(response.data, 'token'),
        // httpOnly: true, // no set, needs to be accessible by the frontend (logout functionality)
        secure: true,
        sameSite: 'lax',
        maxAge: 365 * 24 * 60 * 60,
      })

      cookieStore.set({
        name: process.env.APP_NAME + '.user',
        value: JSON.stringify(get(response.data, 'user')),
        // httpOnly: true, // no set, needs to be accessible by the frontend (logout functionality)
        secure: true,
        sameSite: 'lax',
        maxAge: 365 * 24 * 60 * 60,
      })
    }

    return NextResponse.json(response)
  } catch (error: any) {
    return NextResponse.json(
      {
        success: false,
        message: error.message || 'An error occurred',
      },
      {
        status: 500,
      }
    )
  }
}
