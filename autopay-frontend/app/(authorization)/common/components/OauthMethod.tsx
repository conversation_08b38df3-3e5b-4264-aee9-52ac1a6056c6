'use client'

import { Icons } from '@/components/icons'
import { Button } from '@/components/ui/button'
import { useDomainType } from '@/lib/hooks/useDomainType'
import { queryFetchHelper } from '@/lib/utils/fetchHelper'
import { useMutation } from '@tanstack/react-query'
import { signOut } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useEffect, useRef } from 'react'
import { toast } from 'sonner'

export default function Component({ mode }: { mode: string }) {
  const router = useRouter()
  const popupRef = useRef<Window | null>(null)
  const { isAppDomain } = useDomainType()

  const popupCenter = (url: string, title: string) => {
    const dualScreenLeft = window.screenLeft ?? window.screenX
    const dualScreenTop = window.screenTop ?? window.screenY

    const width = window.innerWidth ?? document.documentElement.clientWidth ?? screen.width

    const height = window.innerHeight ?? document.documentElement.clientHeight ?? screen.height

    const systemZoom = width / window.screen.availWidth

    const left = (width - 500) / 2 / systemZoom + dualScreenLeft
    const top = (height - 550) / 2 / systemZoom + dualScreenTop

    popupRef.current = window.open(
      url,
      title,
      `width=${500 / systemZoom},height=${550 / systemZoom},top=${top},left=${left}`
    )

    if (popupRef.current) {
      popupRef.current.focus()
    }
  }

  const { mutate, isPending } = useMutation({
    mutationFn: (event: MessageEvent) =>
      queryFetchHelper(process.env.NEXT_PUBLIC_APP_URL + '/api/oauth', {
        method: 'POST',
        body: JSON.stringify({
          mode,
          provider: event.data.provider,
          user: event.data.session.user,
        }),
      }),
    onMutate: () => {
      //
    },
    onSuccess: async (data) => {
      toast.success('Chúc mừng!', {
        description: data.message,
      })

      return router.push('/')
    },
    onError: (error) => {
      toast.error(error.message)
    },
    onSettled: async () => {
      // Clear the session for other providers login methods (if not cleared, the session still was the last provider)
      await signOut({ redirect: false })
    },
  })

  useEffect(() => {
    const handleMessage = async (event: MessageEvent) => {
      if (event.origin === window.location.origin && event.data?.action === 'oauth.popup.close') {
        mutate(event)
      }
    }

    window.addEventListener('message', handleMessage)

    return () => {
      window.removeEventListener('message', handleMessage)
    }
  }, [mode, mutate, router])

  async function handleSocialLogin(provider: string) {
    const url = `/oauth/${provider}`
    popupCenter(url, 'Sign In')
  }

  // Only show OAuth options on APP_URL domain
  if (!isAppDomain) {
    return null
  }

  return (
    <div className="grid gap-4">
      <div className="grid grid-cols-1 gap-6">
        {/* <Button
        variant="outline"
        type="button"
        onClick={() => handleSocialLogin('github')}
        className="gap-2"
        disabled={isPending}
        data-tooltip-html={capitalize(mode) + ' with Github'}>
        <Icons.gitHub className="size-4" />
      </Button> */}
        <Button
          variant="outline"
          type="button"
          onClick={() => handleSocialLogin('google')}
          className="gap-2"
          disabled={isPending}
          data-tooltip-html={mode === 'login' ? 'Đăng nhập bằng Google' : 'Đăng ký bằng Google'}>
          <Icons.google className="size-4" />
          {mode === 'login' ? 'Đăng nhập bằng Google' : 'Đăng ký bằng Google'}
        </Button>
      </div>
    </div>
  )
}
