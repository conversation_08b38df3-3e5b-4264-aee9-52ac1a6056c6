'use client'

import { Badge } from '@/components/ui/badge'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import { Skeleton } from '@/components/ui/skeleton'
import { Switch } from '@/components/ui/switch'
import { Textarea } from '@/components/ui/textarea'
import { toast } from '@/components/ui/use-toast'
import useAuth from '@/lib/hooks/useAuth'
import { queryFetchHelper } from '@/lib/utils/fetchHelper'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { AlertCircle, Eye, Loader2, Palette, RotateCcw, Save, Sparkles } from 'lucide-react'
import { useEffect, useState } from 'react'

interface Domain {
  id: string
  organization_id: string
  frontend_hostname?: string
  backend_hostname?: string
  data?: {
    theme?: {
      name?: string
    }
    branding?: any
    seo?: any
    contact?: any
  }
  status: string
  is_active: boolean
}

// Available themes from themes.css
const availableThemes = [
  {
    name: 'red',
    label: 'Đỏ',
    color: '#dc2626',
    gradient: 'from-red-500 to-red-600',
    description: 'Mạnh mẽ và năng động',
  },
  {
    name: 'rose',
    label: 'Hồng',
    color: '#e11d48',
    gradient: 'from-rose-500 to-rose-600',
    description: 'Tinh tế và thanh lịch',
  },
  {
    name: 'orange',
    label: 'Cam',
    color: '#ea580c',
    gradient: 'from-orange-500 to-orange-600',
    description: 'Sáng tạo và nhiệt huyết',
  },
  {
    name: 'green',
    label: 'Xanh lá',
    color: '#16a34a',
    gradient: 'from-green-500 to-green-600',
    description: 'Tự nhiên và thân thiện',
  },
  {
    name: 'blue',
    label: 'Xanh dương',
    color: '#2563eb',
    gradient: 'from-blue-500 to-blue-600',
    description: 'Chuyên nghiệp và tin cậy',
  },
  {
    name: 'yellow',
    label: 'Vàng',
    color: '#ca8a04',
    gradient: 'from-yellow-500 to-yellow-600',
    description: 'Tươi sáng và lạc quan',
  },
  {
    name: 'violet',
    label: 'Tím',
    color: '#7c3aed',
    gradient: 'from-violet-500 to-violet-600',
    description: 'Sáng tạo và độc đáo',
  },
]

const defaultTheme = ''

// Apply theme by adding CSS class to body
const applyThemeColors = (themeName: string): void => {
  const body = document.body
  const existingThemeClasses = Array.from(body.classList).filter((className) => className.startsWith('theme-'))

  existingThemeClasses.forEach((className) => {
    body.classList.remove(className)
  })

  if (themeName) {
    body.classList.add(`theme-${themeName}`)
  }
}

export default function ThemeCustomizationPage() {
  const { user } = useAuth()
  const queryClient = useQueryClient()
  const [selectedTheme, setSelectedTheme] = useState<string>(defaultTheme)

  // Fetch current domain configuration
  const {
    data: domain,
    isLoading: isDomainLoading,
    error: domainError,
  } = useQuery<Domain>({
    queryKey: ['domain', user?.current_organization?.id],
    queryFn: async () => {
      if (!user?.current_organization?.id) {
        throw new Error('Không tìm thấy thông tin tổ chức')
      }
      const response = await queryFetchHelper(`/${user.current_organization.id}/domains`)
      return response.data
    },
    enabled: !!user?.current_organization?.id,
  })

  // Update local state when domain data is loaded
  useEffect(() => {
    if (domain?.data?.theme?.name) {
      setSelectedTheme(domain.data.theme.name)
      // Apply theme immediately when loaded
      applyThemeColors(domain.data.theme.name)
    }
  }, [domain])

  // Apply theme when component mounts with current selectedTheme
  useEffect(() => {
    applyThemeColors(selectedTheme)
  }, [selectedTheme])

  // Save theme configuration mutation
  const { mutate: saveThemeConfig, isPending: isSaving } = useMutation({
    mutationFn: async (data: { theme_name: string }) => {
      if (!user?.current_organization?.id || !domain?.id) {
        throw new Error('Không tìm thấy thông tin domain')
      }

      const updateData = {
        data: {
          ...domain.data,
          theme: {
            ...domain.data?.theme,
            name: data.theme_name,
          },
        },
      }

      return queryFetchHelper(`/${user.current_organization.id}/domains/${domain.id}`, {
        method: 'PUT',
        body: JSON.stringify(updateData),
      })
    },
    onSuccess: () => {
      toast({
        title: 'Lưu cấu hình thành công',
        description: 'Cấu hình theme đã được cập nhật và áp dụng.',
      })
      // Apply theme to ensure it's synced with saved config
      applyThemeColors(selectedTheme)
      // Invalidate domain query to refetch updated data
      queryClient.invalidateQueries({ queryKey: ['domain', user?.current_organization?.id] })
    },
    onError: (error: any) => {
      toast({
        title: 'Lỗi khi lưu cấu hình',
        description: error?.message || 'Đã xảy ra lỗi khi lưu cấu hình theme.',
        variant: 'destructive',
      })
    },
  })

  const handleThemeChange = (themeName: string) => {
    setSelectedTheme(themeName)
    // Apply theme immediately for preview
    applyThemeColors(themeName)
    toast({
      title: 'Theme đã được cập nhật',
      description: 'Thay đổi theme sẽ được áp dụng ngay lập tức.',
    })
  }

  const resetToDefaults = () => {
    setSelectedTheme(defaultTheme)
    // Apply default theme immediately for preview
    applyThemeColors(defaultTheme)
    toast({
      title: 'Đã đặt lại về mặc định',
      description: 'Tất cả cấu hình đã được đặt lại về giá trị mặc định.',
    })
  }

  const saveConfiguration = () => {
    saveThemeConfig({
      theme_name: selectedTheme,
    })
  }

  // Show loading state while fetching domain
  if (isDomainLoading) {
    return (
      <div className="space-y-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="flex items-center gap-2 text-2xl font-bold">
              <Palette className="h-6 w-6" />
              Tùy chỉnh giao diện
            </h1>
            <p className="text-muted-foreground">Đang tải cấu hình theme...</p>
          </div>
        </div>
        <Separator />
        <div className="space-y-4">
          <Skeleton className="h-32 w-full" />
          <Skeleton className="h-64 w-full" />
        </div>
      </div>
    )
  }

  // Show error state if domain fetch failed
  if (domainError) {
    return (
      <div className="space-y-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="flex items-center gap-2 text-2xl font-bold">
              <Palette className="h-6 w-6" />
              Tùy chỉnh giao diện
            </h1>
            <p className="text-muted-foreground">Không thể tải cấu hình theme</p>
          </div>
        </div>
        <Separator />
        <Card>
          <CardContent className="flex items-center gap-3 p-6">
            <AlertCircle className="text-destructive h-5 w-5" />
            <div>
              <p className="font-medium">Lỗi khi tải cấu hình domain</p>
              <p className="text-muted-foreground text-sm">
                {domainError?.message || 'Tổ chức này chưa cấu hình domain. Vui lòng thiết lập domain trước.'}
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="flex items-center gap-2 text-2xl font-bold">
            <Palette className="h-6 w-6" />
            Tùy chỉnh giao diện
          </h1>
          <p className="text-muted-foreground">
            Tùy chỉnh màu sắc và style cho domain của tổ chức. Thay đổi sẽ được áp dụng ngay lập tức.
          </p>
          {domain && (
            <p className="text-muted-foreground mt-1 text-sm">
              Domain:{' '}
              <span className="font-mono">
                {domain.frontend_hostname || domain.backend_hostname || 'Chưa cấu hình'}
              </span>
            </p>
          )}
        </div>

        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={resetToDefaults}
            disabled={isSaving}>
            <RotateCcw className="mr-2 h-4 w-4" />
            Đặt lại
          </Button>
          <Button
            onClick={saveConfiguration}
            disabled={isSaving || !domain}>
            {isSaving ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Đang lưu...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Lưu cấu hình
              </>
            )}
          </Button>
        </div>
      </div>

      <Separator />

      {/* Theme Selector */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Palette className="h-5 w-5" />
            Chọn Theme
          </CardTitle>
          <CardDescription>Chọn một trong các theme có sẵn để thay đổi giao diện của domain</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
            {availableThemes.map((theme) => (
              <div
                key={theme.name}
                className={`group relative cursor-pointer overflow-hidden rounded-xl border-2 transition-all duration-300 hover:scale-[1.02] hover:shadow-lg ${
                  selectedTheme === theme.name
                    ? 'border-primary bg-primary/5 ring-primary/20 shadow-md ring-2'
                    : 'border-border hover:border-primary/50 hover:bg-muted/30'
                }`}
                onClick={() => handleThemeChange(theme.name)}>
                {/* Color Preview */}
                <div className="relative h-20 w-full overflow-hidden">
                  <div className={`h-full w-full bg-gradient-to-br ${theme.gradient} opacity-90`} />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />

                  {/* Selected Badge */}
                  {selectedTheme === theme.name && (
                    <div className="animate-in fade-in-0 zoom-in-95 absolute top-2 right-2 duration-200">
                      <div className="text-primary rounded-full bg-white/90 p-1.5 shadow-sm backdrop-blur-sm">
                        <Sparkles className="h-3 w-3" />
                      </div>
                    </div>
                  )}

                  {/* Hover Effect */}
                  <div className="absolute inset-0 bg-white/10 opacity-0 transition-opacity duration-300 group-hover:opacity-100" />
                </div>

                {/* Theme Info */}
                <div className="space-y-2 p-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-sm font-semibold">{theme.label}</h3>
                    <Badge
                      variant="secondary"
                      className="px-2 py-0.5 text-xs"
                      style={{ backgroundColor: `${theme.color}15`, color: theme.color }}>
                      {theme.name}
                    </Badge>
                  </div>
                  <p className="text-muted-foreground text-xs leading-relaxed">{theme.description}</p>

                  {/* Color Dots */}
                  <div className="flex items-center gap-1.5 pt-1">
                    <div
                      className="h-2 w-2 rounded-full ring-1 ring-white/20"
                      style={{ backgroundColor: theme.color }}
                    />
                    <div
                      className="h-2 w-2 rounded-full ring-1 ring-white/20"
                      style={{ backgroundColor: `${theme.color}80` }}
                    />
                    <div
                      className="h-2 w-2 rounded-full ring-1 ring-white/20"
                      style={{ backgroundColor: `${theme.color}40` }}
                    />
                  </div>
                </div>

                {/* Selection Indicator */}
                {selectedTheme === theme.name && <div className="bg-primary absolute right-0 bottom-0 left-0 h-1" />}
              </div>
            ))}
          </div>

          {/* Current Selection Info */}
          {selectedTheme && (
            <div className="bg-muted/50 mt-6 rounded-lg border p-4">
              <div className="flex items-center gap-3">
                <div
                  className="h-4 w-4 rounded-full shadow-sm ring-2 ring-white"
                  style={{ backgroundColor: availableThemes.find((t) => t.name === selectedTheme)?.color }}
                />
                <div>
                  <p className="text-sm font-medium">
                    Theme hiện tại: {availableThemes.find((t) => t.name === selectedTheme)?.label}
                  </p>
                  <p className="text-muted-foreground text-xs">
                    {availableThemes.find((t) => t.name === selectedTheme)?.description}
                  </p>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <Separator />

      {/* Demo Section */}
      <div className="space-y-6">
        <div className="flex items-center gap-2">
          <Eye className="h-5 w-5" />
          <h2 className="text-xl font-semibold">Demo Elements</h2>
          <Badge
            variant="secondary"
            className="ml-2">
            <Sparkles className="mr-1 h-3 w-3" />
            Live Preview
          </Badge>
        </div>

        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          {/* Demo Navbar */}
          <Card className="demo-card">
            <CardHeader>
              <CardTitle>Navigation Bar</CardTitle>
              <CardDescription>Demo navbar với gradient background và branding</CardDescription>
            </CardHeader>
            <CardContent>
              <div
                className="rounded-lg p-4 text-white"
                style={{
                  background: `linear-gradient(135deg, var(--primary), var(--secondary))`,
                }}>
                <div className="flex items-center justify-between">
                  <h3 className="font-bold">{domain?.data?.branding?.name || 'Brand Name'}</h3>
                  <div className="flex gap-2">
                    <Badge style={{ backgroundColor: 'var(--accent)', color: 'white' }}>New</Badge>
                    <Badge
                      variant="outline"
                      className="border-white text-white">
                      Pro
                    </Badge>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Demo Buttons */}
          <Card className="demo-card">
            <CardHeader>
              <CardTitle>Buttons</CardTitle>
              <CardDescription>Các loại button với custom styling</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-wrap gap-2">
                <Button>Primary Action</Button>
                <Button variant="outline">Secondary Action</Button>
                <Button variant="ghost">Ghost Button</Button>
              </div>
              <div className="flex flex-wrap gap-2">
                <Button size="sm">Small Button</Button>
                <Button size="lg">Large Button</Button>
              </div>
            </CardContent>
          </Card>

          {/* Demo Form */}
          <Card className="demo-card">
            <CardHeader>
              <CardTitle>Form Elements</CardTitle>
              <CardDescription>Input và form controls với theme colors</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="demo-input">Email Address</Label>
                <Input
                  id="demo-input"
                  placeholder="Enter your email"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="demo-textarea">Message</Label>
                <Textarea
                  id="demo-textarea"
                  placeholder="Your message here..."
                  rows={3}
                />
              </div>
              <div className="flex items-center space-x-2">
                <Switch id="demo-switch" />
                <Label htmlFor="demo-switch">Subscribe to newsletter</Label>
              </div>
              <Button className="w-full">Subscribe</Button>
            </CardContent>
          </Card>

          {/* Demo Color Palette */}
          <Card className="demo-card">
            <CardHeader>
              <CardTitle>Color Palette</CardTitle>
              <CardDescription>
                Bảng màu hiện tại của theme: {availableThemes.find((t) => t.name === selectedTheme)?.label}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-4 gap-3">
                <div className="space-y-2 text-center">
                  <div
                    className="h-12 w-full rounded-lg"
                    style={{ backgroundColor: 'var(--primary)' }}></div>
                  <p className="text-xs font-medium">Primary</p>
                  <p className="text-muted-foreground text-xs">CSS Variable</p>
                </div>
                <div className="space-y-2 text-center">
                  <div
                    className="h-12 w-full rounded-lg"
                    style={{ backgroundColor: 'var(--secondary)' }}></div>
                  <p className="text-xs font-medium">Secondary</p>
                  <p className="text-muted-foreground text-xs">CSS Variable</p>
                </div>
                <div className="space-y-2 text-center">
                  <div
                    className="h-12 w-full rounded-lg"
                    style={{ backgroundColor: 'var(--accent)' }}></div>
                  <p className="text-xs font-medium">Accent</p>
                  <p className="text-muted-foreground text-xs">CSS Variable</p>
                </div>
                <div className="space-y-2 text-center">
                  <div className="bg-muted h-12 w-full rounded-lg"></div>
                  <p className="text-xs font-medium">Muted</p>
                  <p className="text-muted-foreground text-xs">System</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Demo Cards Grid */}
        <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
          {[
            { title: 'Dashboard', desc: 'Tổng quan hệ thống', icon: '📊', action: 'Xem chi tiết' },
            { title: 'Analytics', desc: 'Phân tích dữ liệu', icon: '📈', action: 'Báo cáo' },
            { title: 'Settings', desc: 'Cài đặt hệ thống', icon: '⚙️', action: 'Cấu hình' },
          ].map((item, i) => (
            <Card
              key={i}
              className="demo-card">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2 text-lg">
                    <span>{item.icon}</span>
                    {item.title}
                  </CardTitle>
                  <Badge>Featured</Badge>
                </div>
                <CardDescription>{item.desc}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="bg-primary h-2 w-3/4 rounded-full"></div>
                  <div className="bg-secondary h-2 w-1/2 rounded-full"></div>
                  <Button
                    size="sm"
                    className="w-full">
                    {item.action}
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Additional Demo Components */}
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          {/* Demo Alert/Notification */}
          <Card className="demo-card">
            <CardHeader>
              <CardTitle>Notifications & Alerts</CardTitle>
              <CardDescription>Thông báo với theme colors</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="border-primary bg-primary/10 rounded-lg border p-4">
                <div className="flex items-center gap-2">
                  <div className="bg-primary h-2 w-2 rounded-full"></div>
                  <p className="text-primary font-medium">Thông báo thành công</p>
                </div>
                <p className="text-muted-foreground mt-1 text-sm">Cấu hình đã được lưu thành công.</p>
              </div>
              <div className="border-accent bg-accent/10 rounded-lg border p-4">
                <div className="flex items-center gap-2">
                  <div className="bg-accent h-2 w-2 rounded-full"></div>
                  <p className="text-accent font-medium">Cảnh báo</p>
                </div>
                <p className="text-muted-foreground mt-1 text-sm">Vui lòng kiểm tra lại cấu hình.</p>
              </div>
            </CardContent>
          </Card>

          {/* Demo Progress & Stats */}
          <Card className="demo-card">
            <CardHeader>
              <CardTitle>Progress & Statistics</CardTitle>
              <CardDescription>Thanh tiến trình và thống kê</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Hoàn thành</span>
                  <span>75%</span>
                </div>
                <div className="bg-muted h-2 rounded-full">
                  <div className="bg-primary h-2 w-3/4 rounded-full"></div>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Đang xử lý</span>
                  <span>45%</span>
                </div>
                <div className="bg-muted h-2 rounded-full">
                  <div className="bg-accent h-2 w-2/5 rounded-full"></div>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4 pt-2">
                <div className="text-center">
                  <div className="text-primary text-2xl font-bold">1,234</div>
                  <div className="text-muted-foreground text-sm">Tổng số</div>
                </div>
                <div className="text-center">
                  <div className="text-accent text-2xl font-bold">567</div>
                  <div className="text-muted-foreground text-sm">Hoạt động</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
