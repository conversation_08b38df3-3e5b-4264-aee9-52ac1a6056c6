'use client'

import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { toast } from '@/components/ui/use-toast'

const advancedSettingsSchema = z.object({
  seo: z.object({
    title: z.string().optional(),
    description: z.string().optional(),
    keywords: z.string().optional(),
    og_image: z.string().url().optional().or(z.literal('')),
  }),
  contact: z.record(z.any()).optional(),
  custom: z.record(z.any()).optional(),
})

type AdvancedSettingsValues = z.infer<typeof advancedSettingsSchema>

const defaultValues: Partial<AdvancedSettingsValues> = {
  seo: {
    title: '',
    description: '',
    keywords: '',
    og_image: '',
  },
  contact: {},
  custom: {},
}

export function AdvancedSettingsForm() {
  const form = useForm<AdvancedSettingsValues>({
    resolver: zodResolver(advancedSettingsSchema),
    defaultValues,
    mode: 'onChange',
  })

  async function onSubmit(_data: AdvancedSettingsValues) {
    toast({
      title: 'Thông báo',
      description: 'Chức năng cập nhật thiết lập nâng cao đang được phát triển',
    })
    return
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">SEO</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <FormField
              control={form.control}
              name="seo.title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Meta Title</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="AutoPAY - Thanh toán tự động" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="seo.description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Meta Description</FormLabel>
                  <FormControl>
                    <Textarea {...field} placeholder="Mô tả cho SEO..." />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="seo.keywords"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Meta Keywords</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="thanh toán, tự động, autopay" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="seo.og_image"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Open Graph Image URL</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="https://example.com/og-image.jpg" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        <Button type="submit" size="sm">
          Cập nhật thiết lập
        </Button>
      </form>
    </Form>
  )
}
