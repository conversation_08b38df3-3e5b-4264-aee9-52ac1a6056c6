'use client'

import * as React from 'react'
import { Area, AreaChart, CartesianGrid, XAxis } from 'recharts'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import {
  ChartConfig,
  ChartContainer,
  ChartLegend,
  ChartLegendContent,
  ChartTooltip,
  ChartTooltipContent,
} from '@/components/ui/chart'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
const chartData = [
  { date: '2025-03-01', desktop: 222, mobile: 150, visitor: 15 },
  { date: '2025-03-02', desktop: 97, mobile: 180, visitor: 218 },
  { date: '2025-03-03', desktop: 167, mobile: 120, visitor: 212 },
  { date: '2025-03-03', desktop: 242, mobile: 260, visitor: 26 },
  { date: '2025-03-05', desktop: 373, mobile: 290, visitor: 29 },
  { date: '2025-03-06', desktop: 301, mobile: 340, visitor: 234 },
  { date: '2025-03-07', desktop: 245, mobile: 180, visitor: 18 },
  { date: '2025-03-08', desktop: 409, mobile: 320, visitor: 232 },
  { date: '2025-03-09', desktop: 59, mobile: 110, visitor: 11 },
  { date: '2025-03-10', desktop: 261, mobile: 190, visitor: 219 },
  { date: '2025-03-11', desktop: 327, mobile: 350, visitor: 235 },
  { date: '2025-03-12', desktop: 292, mobile: 210, visitor: 221 },
]

const chartConfig = {
  desktop: {
    label: 'Tiền vào',
    color: 'var(--chart-2)',
  },
  mobile: {
    label: 'Tiền ra',
    color: 'var(--chart-1)',
  },
  visitor: {
    label: 'Chênh lệch',
    color: 'var(--chart-4)',
  },
} satisfies ChartConfig

export default function Component() {
  const [timeRange, setTimeRange] = React.useState('30d')

  const filteredData = chartData.filter((item) => {
    const date = new Date(item.date)
    const referenceDate = new Date('2025-03-01')
    let daysToSubtract = 90
    if (timeRange === '30d') {
      daysToSubtract = 0
    } else if (timeRange === '7d') {
      daysToSubtract = 7
    }
    const startDate = new Date(referenceDate)
    startDate.setDate(startDate.getDate() - daysToSubtract)
    return date >= startDate
  })

  return (
    <Card className="py-0">
      <CardHeader className="flex items-center gap-2 space-y-0 border-b py-5 sm:flex-row">
        <div className="grid flex-1 gap-1 text-center sm:text-left">
          <CardTitle>Dòng tiền trong tháng</CardTitle>
          <CardDescription>Biểu đồ dòng tiền từng ngày trong tháng</CardDescription>
        </div>
        <Select
          value={timeRange}
          onValueChange={setTimeRange}>
          <SelectTrigger
            className="w-[160px] rounded-lg sm:ml-auto"
            aria-label="Select a value">
            <SelectValue placeholder="Last 3 months" />
          </SelectTrigger>
          <SelectContent className="rounded-xl">
            <SelectItem
              value="30d"
              className="rounded-lg">
              Tháng này
            </SelectItem>
            <SelectItem
              value="90d"
              className="rounded-lg">
              Last 90 days
            </SelectItem>
            <SelectItem
              value="7d"
              className="rounded-lg">
              Last 7 days
            </SelectItem>
          </SelectContent>
        </Select>
      </CardHeader>
      <CardContent className="px-0">
        <ChartContainer
          config={chartConfig}
          className="aspect-auto h-[250px] w-full">
          <AreaChart data={filteredData}>
            <defs>
              <linearGradient
                id="fillVisitor"
                x1="0"
                y1="0"
                x2="0"
                y2="1">
                <stop
                  offset="5%"
                  stopColor="var(--color-visitor)"
                  stopOpacity={0.8}
                />
                <stop
                  offset="95%"
                  stopColor="var(--color-visitor)"
                  stopOpacity={0.1}
                />
              </linearGradient>
              <linearGradient
                id="fillDesktop"
                x1="0"
                y1="0"
                x2="0"
                y2="1">
                <stop
                  offset="5%"
                  stopColor="var(--color-desktop)"
                  stopOpacity={0.8}
                />
                <stop
                  offset="95%"
                  stopColor="var(--color-desktop)"
                  stopOpacity={0.1}
                />
              </linearGradient>
              <linearGradient
                id="fillMobile"
                x1="0"
                y1="0"
                x2="0"
                y2="1">
                <stop
                  offset="5%"
                  stopColor="var(--color-mobile)"
                  stopOpacity={0.8}
                />
                <stop
                  offset="95%"
                  stopColor="var(--color-mobile)"
                  stopOpacity={0.1}
                />
              </linearGradient>
            </defs>
            <CartesianGrid vertical={false} />
            <XAxis
              dataKey="date"
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              minTickGap={32}
              tickFormatter={(value) => {
                const date = new Date(value)
                return date.toLocaleDateString('vi-VN', {
                  month: 'numeric',
                  day: 'numeric',
                })
              }}
            />
            <ChartTooltip
              cursor={false}
              content={
                <ChartTooltipContent
                  labelFormatter={(value) => {
                    return new Date(value).toLocaleDateString('en-US', {
                      month: 'short',
                      day: 'numeric',
                    })
                  }}
                  indicator="dot"
                />
              }
            />
            <Area
              dataKey="desktop"
              type="natural"
              fill="url(#fillDesktop)"
              stroke="var(--color-desktop)"
              stackId="a"
            />
            <Area
              dataKey="mobile"
              type="natural"
              fill="url(#fillMobile)"
              stroke="var(--color-mobile)"
              stackId="a"
            />
            <Area
              dataKey="visitor"
              type="natural"
              fill="url(#fillVisitor)"
              stroke="var(--color-visitor)"
            />
            <ChartLegend
              content={
                <ChartLegendContent
                  payload={[]}
                  verticalAlign="top"
                />
              }
            />
          </AreaChart>
        </ChartContainer>
      </CardContent>
    </Card>
  )
}
