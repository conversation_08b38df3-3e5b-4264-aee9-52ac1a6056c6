'use client'

import { <PERSON>, <PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON>ist, XAxis } from 'recharts'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  ChartConfig,
  ChartContainer,
  ChartLegend,
  ChartLegendContent,
  ChartTooltip,
  ChartTooltipContent,
} from '@/components/ui/chart'
const chartData = [
  { month: 'Thứ 2', desktop: 186, mobile: 80 },
  { month: 'Thứ 3', desktop: 305, mobile: 200 },
  { month: 'Thứ 4', desktop: 237, mobile: 120 },
  { month: 'Thứ 5', desktop: 73, mobile: 190 },
  { month: 'Thứ 6', desktop: 209, mobile: 130 },
  { month: 'Thứ 7', desktop: 214, mobile: 140 },
  { month: 'Chủ Nhật', desktop: 214, mobile: 140 },
]

const chartConfig = {
  desktop: {
    label: 'Tiền vào',
    color: 'var(--chart-1)',
  },
  mobile: {
    label: 'Tiền ra',
    color: 'var(--chart-4)',
  },
} satisfies ChartConfig

export default function Component() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Giao dịch trong tuần</CardTitle>
      </CardHeader>
      <CardContent>
        <ChartContainer
          config={chartConfig}
          className="md:h-60 md:w-full">
          <BarChart
            accessibilityLayer
            data={chartData}>
            <CartesianGrid vertical={false} />
            <XAxis
              dataKey="month"
              tickLine={false}
              tickMargin={10}
              axisLine={false}
            />
            <ChartTooltip
              cursor={false}
              content={<ChartTooltipContent indicator="dashed" />}
            />
            <ChartLegend content={<ChartLegendContent />} />
            <Bar
              dataKey="desktop"
              fill="var(--color-desktop)"
              radius={4}
              barSize={30}>
              <LabelList
                position="top"
                offset={5}
                className="fill-foreground"
                fontSize={12}
              />
            </Bar>
            <Bar
              dataKey="mobile"
              fill="var(--color-mobile)"
              radius={4}
              barSize={30}>
              <LabelList
                position="top"
                offset={5}
                className="fill-foreground"
                fontSize={12}
              />
            </Bar>
          </BarChart>
        </ChartContainer>
      </CardContent>
    </Card>
  )
}
