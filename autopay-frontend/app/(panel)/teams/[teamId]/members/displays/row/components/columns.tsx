import { ColumnDef } from '@tanstack/react-table'

import { LiaUserMinusSolid } from 'react-icons/lia'

import { IoMdPaperPlane } from 'react-icons/io'

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { CopyIcon } from '@radix-ui/react-icons'

export const columns: ColumnDef<Member>[] = [
  {
    accessorKey: 'name',
    header: ({ column }) => <></>,
    cell: ({ row }) => {
      return (
        <div className="flex items-center justify-between">
          <div className="group flex items-center space-x-2">
            <Avatar className="h-8 w-8">
              <AvatarImage
                src={row.original.avatar}
                alt={row.getValue('name')}
                className="rounded-full border"
              />
              <AvatarFallback>{row.original.avatar}</AvatarFallback>
            </Avatar>
            <span className="font-medium capitalize">{row.getValue('name')}</span>
            <span className="text-muted-foreground hidden lowercase md:block">{row.original.email}</span>
            <Button
              size="icon"
              variant="outline"
              className="h-6 w-6 opacity-0 transition-opacity group-hover:opacity-100"
              data-tooltip-html="Copy">
              <CopyIcon className="h-3 w-3" />
            </Button>
          </div>
          <div className="text-muted-foreground flex items-center text-xs">
            <span className="hidden md:block">Invitation pending...</span>
            <Button
              variant="ghost"
              size="icon"
              className="ml-1 flex h-6 w-6 p-0"
              data-tooltip-html="Resend Invitation">
              <IoMdPaperPlane className="h-5 w-5" />
            </Button>
          </div>
        </div>
      )
    },
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: 'role',
    header: ({ column }) => <></>,
    cell: ({ row }) => {
      return (
        <div className="flex items-center justify-end">
          <Select defaultValue="superadmin">
            <SelectTrigger className="bg-muted w-fit px-2 py-0">
              <SelectValue placeholder="Role" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="superadmin">Admin</SelectItem>
              <SelectItem value="dark">Moderator</SelectItem>
              <SelectItem value="system">Member</SelectItem>
            </SelectContent>
          </Select>
        </div>
      )
    },
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id))
    },
    enableSorting: false,
    enableHiding: false,
  },
  {
    id: 'actions',
    cell: ({ row }) => (
      <div className="flex items-center justify-end">
        <Button
          variant="ghost"
          size="icon"
          className="group hover:bg-red-50"
          data-tooltip-html="Revoke">
          <LiaUserMinusSolid className="text-muted-foreground group-hover:text-destructive h-5 w-5" />
        </Button>
      </div>
    ),
  },
]
