import { ColumnDef } from '@tanstack/react-table'

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import { Wrench } from 'lucide-react'
import Link from 'next/link'
import { RiVipCrown2Line } from 'react-icons/ri'
import { DataTableColumnHeader } from './data-table-column-header'
import { DataTableRowActions } from './data-table-row-actions'

export const columns: ColumnDef<Role>[] = [
  {
    accessorKey: 'name',
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title="Name"
      />
    ),
    cell: ({ row }) => {
      return (
        <div className="group flex items-center space-x-2">
          <RiVipCrown2Line className="h-4 w-4" />
          <span className="font-medium">{row.getValue('name')}</span>
          <Button
            size="icon"
            variant="outline"
            className="h-6 w-6 opacity-0 transition-opacity group-hover:opacity-100"
            data-tooltip-html="Permissions"
            asChild>
            <Link href={`/teams/${row.original.id}/roles/permissions`}>
              <Wrench className="h-3 w-3" />
            </Link>
          </Button>
        </div>
      )
    },
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: 'members',
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title=""
        className="text-center"
      />
    ),
    cell: ({ row }) => {
      if (!row.getValue('members_count')) return null

      const members = row.getValue<Member[]>('members')
      const otherCount = parseInt(row.getValue('members_count')) - 3

      return (
        <div className="flex items-center">
          <ul className="flex -space-x-1">
            {members.map((member) => (
              <li
                className="flex items-center justify-between"
                key={member.avatar}>
                <Avatar className="hidden h-6 w-6 sm:flex">
                  <AvatarImage
                    src={member.avatar}
                    alt="Avatar"
                    className="rounded-full border"
                  />
                  <AvatarFallback>{member.name}</AvatarFallback>
                </Avatar>
              </li>
            ))}
            {otherCount > 0 && (
              <li className="ml-1! flex items-center justify-between">
                <Avatar className="hidden h-6 w-6 sm:flex">
                  <AvatarFallback>+{otherCount}</AvatarFallback>
                </Avatar>
              </li>
            )}
          </ul>
        </div>
      )
    },
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id))
    },
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: 'members_count',
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title="Members"
        className="text-center"
      />
    ),
    cell: ({ row }) => {
      return <div className="flex items-center justify-center">{row.getValue('members_count')}</div>
    },
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id))
    },
    enableSorting: false,
    enableHiding: false,
  },
  {
    id: 'actions',
    cell: ({ row }) => (
      <div className="flex items-center justify-end">
        <DataTableRowActions row={row} />
      </div>
    ),
  },
]
