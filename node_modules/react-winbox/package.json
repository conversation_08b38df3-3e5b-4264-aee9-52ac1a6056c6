{"name": "react-winbox", "version": "1.5.0", "description": "The React component for WinBox.js. Full Reactful props and state. A window manager for React", "private": false, "homepage": "https://github.com/rickonono3/react-winbox", "repository": {"type": "git", "url": "github:rickonono3/react-winbox"}, "main": "dist/index.js", "types": "dist/index.d.ts", "license": "MIT", "dependencies": {"winbox": "=0.2.6"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "devDependencies": {"@types/react": "^18.0.9", "@types/react-dom": "^18.0.4", "@typescript-eslint/eslint-plugin": "^5.19.0", "@typescript-eslint/parser": "^5.19.0", "eslint-plugin-react": "^7.29.4", "typescript": "^4.6.3"}, "scripts": {"build": "tsc"}, "eslintConfig": {"extends": ["react-app"]}, "files": ["/dist"]}