/**
 * WinBox.js v0.2.6
 * Copyright 2022 Nextapps GmbH
 * Author: <PERSON>
 * Licence: Apache-2.0
 * https://github.com/nextapps-de/winbox
 */
(function(){'use strict';var f,aa=document.createElement("div");aa.innerHTML="<div class=wb-header><div class=wb-control><span class=wb-min></span><span class=wb-max></span><span class=wb-full></span><span class=wb-close></span></div><div class=wb-drag><div class=wb-icon></div><div class=wb-title></div></div></div><div class=wb-body></div><div class=wb-n></div><div class=wb-s></div><div class=wb-w></div><div class=wb-e></div><div class=wb-nw></div><div class=wb-ne></div><div class=wb-se></div><div class=wb-sw></div>";function k(a,b,c,e){a&&a.addEventListener(b,c,e||!1)}function m(a,b,c){var e=window;e&&e.removeEventListener(a,b,c||!1)}function p(a){a.stopPropagation();a.cancelable&&a.preventDefault()}function r(a,b,c){c=""+c;a["_s_"+b]!==c&&(a.style.setProperty(b,c),a["_s_"+b]=c)};var w=[],x={capture:!0,passive:!0},y,ba=0,z=10,A,J,K,da,L,P;
function Q(a,b){if(!(this instanceof Q))return new Q(a);y||ea();var c;if(a){if(b){var e=a;a=b}if("string"===typeof a)e=a;else{var d=a.id;var g=a.index;var l=a.root;var F=a.template;e=e||a.title;var t=a.icon;var R=a.mount;var h=a.html;var B=a.url;var n=a.width;var q=a.height;var C=a.minwidth;var D=a.minheight;var u=a.maxwidth;var v=a.maxheight;var E=a.autosize;var G=a.min;var H=a.max;var I=a.hidden;var ca=a.modal;var X=a.x||(ca?"center":0);var Y=a.y||(ca?"center":0);var M=a.top;var N=a.left;var S=
a.bottom;var T=a.right;var ha=a.background;var O=a.border;var U=a.header;var Z=a["class"];var la=a.onclose;var ma=a.onfocus;var na=a.onblur;var oa=a.onmove;var pa=a.onresize;var qa=a.onfullscreen;var ra=a.onmaximize;var sa=a.onminimize;var ta=a.onrestore;var ua=a.onhide;var va=a.onshow;var wa=a.onload}}this.g=(F||aa).cloneNode(!0);this.g.id=this.id=d||"winbox-"+ ++ba;this.g.className="winbox"+(Z?" "+("string"===typeof Z?Z:Z.join(" ")):"")+(ca?" modal":"");this.g.winbox=this;this.window=this.g;this.body=
this.g.getElementsByClassName("wb-body")[0];this.h=U||35;ha&&this.setBackground(ha);O?r(this.body,"margin",O+(isNaN(O)?"":"px")):O=0;U&&(b=this.g.getElementsByClassName("wb-header")[0],r(b,"height",U+"px"),r(b,"line-height",U+"px"),r(this.body,"top",U+"px"));e&&this.setTitle(e);t&&this.setIcon(t);R?this.mount(R):h?this.body.innerHTML=h:B&&this.setUrl(B,wa);M=M?V(M,P):0;S=S?V(S,P):0;N=N?V(N,L):0;T=T?V(T,L):0;e=L-N-T;t=P-M-S;u=u?V(u,e):e;v=v?V(v,t):t;C=C?V(C,u):150;D=D?V(D,v):this.h;E?((l||y).appendChild(this.body),
n=Math.max(Math.min(this.body.clientWidth+2*O+1,u),C),q=Math.max(Math.min(this.body.clientHeight+this.h+O+1,v),D),this.g.appendChild(this.body)):(n=n?V(n,u):Math.max(u/2,C)|0,q=q?V(q,v):Math.max(v/2,D)|0);X=X?V(X,e,n):N;Y=Y?V(Y,t,q):M;this.x=X;this.y=Y;this.width=n;this.height=q;this.o=C;this.m=D;this.l=u;this.j=v;this.top=M;this.right=T;this.bottom=S;this.left=N;this.index=g;this.focused=this.hidden=this.full=this.max=this.min=!1;this.onclose=la;this.onfocus=ma;this.onblur=na;this.onmove=oa;this.onresize=
pa;this.onfullscreen=qa;this.onmaximize=ra;this.onminimize=sa;this.onrestore=ta;this.onhide=ua;this.onshow=va;H?this.maximize():G?this.minimize():this.resize().move();if(I)this.hide();else if(this.focus(),g||0===g)this.index=g,r(this.g,"z-index",g),g>z&&(z=g);fa(this);(l||y).appendChild(this.g);(c=a.oncreate)&&c.call(this,a)}Q["new"]=function(a){return new Q(a)};
function V(a,b,c){"string"===typeof a&&("center"===a?a=(b-c)/2|0:"right"===a||"bottom"===a?a=b-c:(c=parseFloat(a),a="%"===(""+c!==a&&a.substring((""+c).length))?b/100*c|0:c));return a}function ea(){y=document.body;y[K="requestFullscreen"]||y[K="msRequestFullscreen"]||y[K="webkitRequestFullscreen"]||y[K="mozRequestFullscreen"]||(K="");da=K&&K.replace("request","exit").replace("mozRequest","mozCancel").replace("Request","Exit");k(window,"resize",function(){ia();ja()});ia()}
function fa(a){W(a,"drag");W(a,"n");W(a,"s");W(a,"w");W(a,"e");W(a,"nw");W(a,"ne");W(a,"se");W(a,"sw");k(a.g.getElementsByClassName("wb-min")[0],"click",function(b){p(b);a.min?a.focus().restore():a.blur().minimize()});k(a.g.getElementsByClassName("wb-max")[0],"click",function(){a.max?a.restore():a.maximize()});K?k(a.g.getElementsByClassName("wb-full")[0],"click",function(){a.fullscreen()}):a.addClass("no-full");k(a.g.getElementsByClassName("wb-close")[0],"click",function(b){p(b);a.close()||(a=null)});
k(a.g,"click",function(){a.focus()})}function ka(a){w.splice(w.indexOf(a),1);ja();a.removeClass("min");a.min=!1;a.g.title=""}function ja(){for(var a=w.length,b={},c={},e=0,d;e<a;e++)d=w[e],d=(d.left||d.right)+":"+(d.top||d.bottom),c[d]?c[d]++:(b[d]=0,c[d]=1);e=0;for(var g,l;e<a;e++)d=w[e],g=(d.left||d.right)+":"+(d.top||d.bottom),l=Math.min((L-d.left-d.right)/c[g],250),d.resize(l+1|0,d.h,!0).move(d.left+b[g]*l|0,P-d.bottom-d.h,!0),b[g]++}
function W(a,b){function c(h){p(h);a.focus();if("drag"===b){if(a.min){a.restore();return}var B=Date.now(),n=B-R;R=B;if(300>n){a.max?a.restore():a.maximize();return}}a.max||a.min||(y.classList.add("wb-lock"),(l=h.touches)&&(l=l[0])?(h=l,k(window,"touchmove",e,x),k(window,"touchend",d,x)):(k(window,"mousemove",e),k(window,"mouseup",d)),F=h.pageX,t=h.pageY)}function e(h){p(h);l&&(h=h.touches[0]);var B=h.pageX;h=h.pageY;var n=B-F,q=h-t,C=a.width,D=a.height,u=a.x,v=a.y,E;if("drag"===b){a.x+=n;a.y+=q;var G=
E=1}else{if("e"===b||"se"===b||"ne"===b){a.width+=n;var H=1}else if("w"===b||"sw"===b||"nw"===b)a.x+=n,a.width-=n,G=H=1;if("s"===b||"se"===b||"sw"===b){a.height+=q;var I=1}else if("n"===b||"ne"===b||"nw"===b)a.y+=q,a.height-=q,E=I=1}H&&(a.width=Math.max(Math.min(a.width,a.l,L-a.x-a.right),a.o),H=a.width!==C);I&&(a.height=Math.max(Math.min(a.height,a.j,P-a.y-a.bottom),a.m),I=a.height!==D);(H||I)&&a.resize();G&&(a.x=Math.max(Math.min(a.x,L-a.width-a.right),a.left),G=a.x!==u);E&&(a.y=Math.max(Math.min(a.y,
P-a.height-a.bottom),a.top),E=a.y!==v);(G||E)&&a.move();if(H||G)F=B;if(I||E)t=h}function d(h){p(h);y.classList.remove("wb-lock");l?(m("touchmove",e,x),m("touchend",d,x)):(m("mousemove",e),m("mouseup",d))}var g=a.g.getElementsByClassName("wb-"+b)[0];if(g){var l,F,t,R=0;k(g,"mousedown",c);k(g,"touchstart",c,x)}}function ia(){var a=document.documentElement;L=a.clientWidth;P=a.clientHeight}f=Q.prototype;
f.mount=function(a){this.unmount();a.i||(a.i=a.parentNode);this.body.textContent="";this.body.appendChild(a);return this};f.unmount=function(a){var b=this.body.firstChild;if(b){var c=a||b.i;c&&c.appendChild(b);b.i=a}return this};f.setTitle=function(a){var b=this.g.getElementsByClassName("wb-title")[0];a=this.title=a;var c=b.firstChild;c?c.nodeValue=a:b.textContent=a;return this};
f.setIcon=function(a){var b=this.g.getElementsByClassName("wb-icon")[0];r(b,"background-image","url("+a+")");r(b,"display","inline-block");return this};f.setBackground=function(a){r(this.g,"background",a);return this};f.setUrl=function(a,b){var c=this.body.firstChild;c&&"iframe"===c.tagName.toLowerCase()?c.src=a:(this.body.innerHTML='<iframe src="'+a+'"></iframe>',b&&(this.body.firstChild.onload=b));return this};
f.focus=function(a){if(!1===a)return this.blur();J!==this&&this.g&&(J&&J.blur(),r(this.g,"z-index",++z),this.index=z,this.addClass("focus"),J=this,this.focused=!0,this.onfocus&&this.onfocus());return this};f.blur=function(a){if(!1===a)return this.focus();J===this&&(this.removeClass("focus"),this.focused=!1,this.onblur&&this.onblur(),J=null);return this};f.hide=function(a){if(!1===a)return this.show();if(!this.hidden)return this.onhide&&this.onhide(),this.hidden=!0,this.addClass("hide")};
f.show=function(a){if(!1===a)return this.hide();if(this.hidden)return this.onshow&&this.onshow(),this.hidden=!1,this.removeClass("hide")};f.minimize=function(a){if(!1===a)return this.restore();A&&xa();this.max&&(this.removeClass("max"),this.max=!1);this.min||(w.push(this),ja(),this.g.title=this.title,this.addClass("min"),this.min=!0,this.onminimize&&this.onminimize());return this};
f.restore=function(){A&&xa();this.min&&(ka(this),this.resize().move(),this.onrestore&&this.onrestore());this.max&&(this.max=!1,this.removeClass("max").resize().move(),this.onrestore&&this.onrestore());return this};f.maximize=function(a){if(!1===a)return this.restore();A&&xa();this.min&&ka(this);this.max||(this.addClass("max").resize(L-this.left-this.right,P-this.top-this.bottom,!0).move(this.left,this.top,!0),this.max=!0,this.onmaximize&&this.onmaximize());return this};
f.fullscreen=function(a){this.min&&(ka(this),this.resize().move());if(!A||!xa())this.body[K](),A=this,this.full=!0,this.onfullscreen&&this.onfullscreen();else if(!1===a)return this.restore();return this};function xa(){A.full=!1;if(document.fullscreen||document.fullscreenElement||document.webkitFullscreenElement||document.mozFullScreenElement)return document[da](),!0}
f.close=function(a){if(this.onclose&&this.onclose(a))return!0;this.min&&ka(this);this.unmount();this.g.remove();this.g.textContent="";this.g=this.body=this.g.winbox=null;J===this&&(J=null)};f.move=function(a,b,c){a||0===a?c||(this.x=a?a=V(a,L-this.left-this.right,this.width):0,this.y=b?b=V(b,P-this.top-this.bottom,this.height):0):(a=this.x,b=this.y);r(this.g,"left",a+"px");r(this.g,"top",b+"px");this.onmove&&this.onmove(a,b);return this};
f.resize=function(a,b,c){a||0===a?c||(this.width=a?a=V(a,this.l):0,this.height=b?b=V(b,this.j):0,a=Math.max(a,this.o),b=Math.max(b,this.m)):(a=this.width,b=this.height);r(this.g,"width",a+"px");r(this.g,"height",b+"px");this.onresize&&this.onresize(a,b);return this};
f.addControl=function(a){var b=a["class"],c=a.image,e=a.click;a=a.index;var d=document.createElement("span"),g=this.g.getElementsByClassName("wb-control")[0],l=this;b&&(d.className=b);c&&r(d,"background-image","url("+c+")");e&&(d.onclick=function(F){e.call(this,F,l)});g.insertBefore(d,g.childNodes[a||0]);return this};f.removeControl=function(a){(a=this.g.getElementsByClassName(a)[0])&&a.remove();return this};f.addClass=function(a){this.g.classList.add(a);return this};
f.removeClass=function(a){this.g.classList.remove(a);return this};f.toggleClass=function(a){return this.g.classList.contains(a)?this.removeClass(a):this.addClass(a)};window.WinBox=Q;}).call(this);
