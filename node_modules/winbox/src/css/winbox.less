@import "images.less";
@winbox-header-height: 35px;

.winbox {
  position: fixed;
  left: 0;
  top: 0;
  background: #0050ff;
  box-shadow: 0 14px 28px rgba(0, 0, 0, 0.25), 0 10px 10px rgba(0, 0, 0, 0.22);
  /* using transform make contents blur when applied and requires more gpu memory */
  //transition: width .3s, height .3s, transform .3s;
  transition: width .3s, height .3s, left .3s, top .3s;
  transition-timing-function: cubic-bezier(0.3, 1, 0.3, 1);
  //transform-origin: bottom center;
  /* contain "strict" does not make overflow contents selectable */
  contain: layout size;
  /* explicitly set text align to left fixes an issue with iframes alignment when centered */
  text-align: left;
  /* workaround for using passive listeners */
  touch-action: none;
}

.wb-header {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: @winbox-header-height;
  line-height: @winbox-header-height;
  color: #fff;
  overflow: hidden;
  z-index: 1;
}

.wb-body {
  position: absolute;
  top: @winbox-header-height;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
  overflow-scrolling: touch;
  will-change: contents;
  background: #fff;
  /* when no border is set there is some thin line visible */
  //margin: -1px;
  /* always hide top border visually */
  margin-top: 0 !important;
  contain: strict;
  //content-visibility: auto;
  z-index: 0;
}

body > .wb-body{
  position: relative;
  display: inline-block;
  visibility: hidden;
  contain: none;
}

.wb-drag{
  height: 100%;
  padding-left: 10px;
  cursor: move;
}

.wb-title{
  font-family : Arial, sans-serif;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.wb-icon {
  display: none;
  width: 20px;
  height: 100%;
  margin: -1px 8px 0 -3px;
  float: left;
  background-repeat: no-repeat;
  background-size: 100%;
  background-position: center;
}

.wb-n {
  position: absolute;
  top: -5px;
  left: 0;
  right: 0;
  height: 10px;
  cursor: n-resize;
  z-index: 2;
}

.wb-e {
  position: absolute;
  top: 0;
  right: -5px;
  bottom: 0;
  width: 10px;
  cursor: w-resize;
  z-index: 2;
}

.wb-s {
  position: absolute;
  bottom: -5px;
  left: 0;
  right: 0;
  height: 10px;
  cursor: n-resize;
  z-index: 2;
}

.wb-w {
  position: absolute;
  top: 0;
  left: -5px;
  bottom: 0;
  width: 10px;
  cursor: w-resize;
  z-index: 2;
}

.wb-nw {
  position: absolute;
  top: -5px;
  left: -5px;
  width: 15px;
  height: 15px;
  cursor: nw-resize;
  z-index: 2;
}

.wb-ne {
  position: absolute;
  top: -5px;
  right: -5px;
  width: 15px;
  height: 15px;
  cursor: ne-resize;
  z-index: 2;
}

.wb-sw {
  position: absolute;
  bottom: -5px;
  left: -5px;
  width: 15px;
  height: 15px;
  cursor: ne-resize;
  z-index: 2;
}

.wb-se {
  position: absolute;
  bottom: -5px;
  right: -5px;
  width: 15px;
  height: 15px;
  cursor: nw-resize;
  z-index: 2;
}

.wb-control{
  float: right;
  height: 100%;
  max-width: 100%;
  text-align: center;
}

.wb-control * {
  display: inline-block;
  width: 30px;
  height: 100%;
  max-width: 100%;
  background-position: center;
  background-repeat: no-repeat;
  cursor: pointer;
}

.wb-min {
  background-image: url(@min);
  background-size: 14px auto;
  background-position: center calc(50% + 6px);
}

.wb-max {
  background-image: url(@max);
  background-size: 17px auto;
}

.wb-close {
  background-image: url(@close);
  background-size: 15px auto;
  background-position: 5px center;
}

.wb-full {
  background-image: url(@full);
  background-size: 16px auto;
}

/*
.winbox:not(.max) .wb-max {
  background-image: url(@restore);
  background-size: 20px auto;
  background-position: center bottom 5px;
}
*/

/*
.winbox:fullscreen{
  transition: none !important;
}
.winbox:fullscreen .wb-full{
  background-image: url(@minimize);
}
.winbox:fullscreen > div,
.winbox:fullscreen .wb-title,
*/

.winbox.modal .wb-body ~ div,
.winbox.modal .wb-drag,
.winbox.min .wb-body ~ div,
.winbox.max .wb-body ~ div {
  pointer-events: none;
}
.winbox.max .wb-drag{
  cursor: default;
}

.winbox.min {
  .wb-full,
  .wb-min {
    display: none;
  }
  .wb-drag {
    cursor: default;
  }
  .wb-body > *{
    display: none;
  }
}

.winbox.hide {
  display: none;
  //visibility: hidden;
}

.winbox.max {
  box-shadow: none;
}

.winbox.max .wb-body {
  margin: 0 !important;
}

.winbox iframe{
  position: absolute;
  width: 100%;
  height: 100%;
  border: 0;
}

body.wb-lock .winbox{
  will-change: left, top, width, height;
  transition: none;
}
body.wb-lock iframe {
  pointer-events: none;
}

.winbox.modal{
  &:before{
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: inherit;
    border-radius: inherit;
  }
  &:after{
    content: '';
    position: absolute;
    top: -50vh;
    left: -50vw;
    right: -50vw;
    bottom: -50vh;
    background: #0d1117;
    animation: wb-fade-in 0.2s ease-out forwards;
    z-index: -1;
  }
  .wb-min,
  .wb-max,
  .wb-full{
    display: none;
  }
}

@keyframes wb-fade-in {
  0%{
    opacity: 0;
  }
  100%{
    opacity: 0.85;
  }
}

@import "control.less";
