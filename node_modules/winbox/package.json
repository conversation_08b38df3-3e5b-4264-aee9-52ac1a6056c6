{"name": "winbox", "version": "0.2.6", "description": "Modern HTML5 window manager for the web.", "homepage": "https://nextapps-de.github.io/winbox/", "author": "<PERSON>", "copyright": "Nextapps GmbH", "license": "Apache-2.0", "keywords": ["windowmanager", "windows", "popup", "window-manager", "desktop", "window", "frame", "box"], "bugs": {"url": "https://github.com/nextapps-de/winbox/issues", "email": "<EMAIL>"}, "main": "src/js/winbox.js", "type": "module", "browser": "dist/winbox.bundle.min.js", "preferGlobal": false, "repository": {"type": "git", "url": "https://github.com/nextapps-de/winbox.git"}, "scripts": {"copy": "cpx \"src/img/*.*\" dist/img/", "clean": "node task/clean", "build:css": "lessc src/css/winbox.less src/css/winbox.css", "build:css:bundle": "node task/bundle --image && lessc --autoprefix=\">=1%\" --clean-css=\"--s1 --advanced --rebase\" tmp/bundle.less dist/css/winbox.min.css && csso dist/css/winbox.min.css --output dist/css/winbox.min.css", "build:css:theme-modern": "lessc --autoprefix=\">=1%\" --clean-css=\"--s1 --advanced --rebase\" src/css/themes/modern.less dist/css/themes/modern.min.css && csso dist/css/themes/modern.min.css --output dist/css/themes/modern.min.css", "build:css:theme-white": "lessc --autoprefix=\">=1%\" --clean-css=\"--s1 --advanced --rebase\" src/css/themes/white.less dist/css/themes/white.min.css && csso dist/css/themes/white.min.css --output dist/css/themes/white.min.css", "build:js": "node task/build", "build:bundle": "node task/build --bundle", "build:svg": "node task/svgo", "build": "npm run clean && npm run copy && npm run build:svg && npm run build:css && npm run build:css:bundle && npm run build:css:theme-modern && npm run build:css:theme-white && node task/bundle --style && npm run build:js && npm run build:bundle && echo Build Complete. && exit 0", "server": "node task/server.js"}, "files": ["dist/", "src/", "task/", "README.md", "CHANGELOG.md", "LICENSE"], "readme": "README.md", "dependencies": {}, "devDependencies": {"base64-img": "^1.0.4", "cpx": "^1.5.0", "csso": "^5.0.4", "csso-cli": "^4.0.1", "google-closure-compiler": "^20220719.0.0", "less": "^4.1.3", "less-plugin-autoprefix": "^2.0.0", "less-plugin-clean-css": "^1.5.1", "svgo": "^2.8.0", "web-servo": "^0.5.1"}}