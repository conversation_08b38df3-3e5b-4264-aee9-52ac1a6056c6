<?php

namespace Modules\Organization\Http\Requests;

use Modules\Core\Http\Requests\BaseFormRequest;

class StoreDomainRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'frontend_hostname' => 'nullable|string|max:255',
            'backend_hostname' => 'nullable|string|max:255',
            'data' => 'nullable|array',
            'data.branding' => 'nullable|array',
            'data.branding.name' => 'nullable|string|max:255',
            'data.branding.slogan' => 'nullable|string|max:500',
            'data.branding.logo_url' => 'nullable|url',
            'data.branding.favicon_url' => 'nullable|url',
            'data.theme' => 'nullable|array',
            'data.theme.name' => 'nullable|string|max:255',
            'data.seo' => 'nullable|array',
            'data.seo.title' => 'nullable|string|max:255',
            'data.seo.description' => 'nullable|string|max:500',
            'data.seo.keywords' => 'nullable|string|max:500',
            'data.seo.og_image' => 'nullable|url',
            'data.contact' => 'nullable|array',
            'data.custom' => 'nullable|array',
            'is_active' => 'nullable|boolean',
            'organization_id' => 'nullable|exists:organizations,id',
        ];
    }
}
