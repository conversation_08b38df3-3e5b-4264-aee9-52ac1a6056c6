<?php

namespace Modules\Organization\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Modules\Organization\Models\Domain;
use Symfony\Component\HttpFoundation\Response;

class ResolveDomainOrganization
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        $hostname = $request->getHost();

        // Skip resolution for API routes or admin routes
        if ($this->shouldSkipResolution($request)) {
            return $next($request);
        }

        // Determine a domain type first
        $domainType = $this->determineDomainType($hostname);
        $request->attributes->set('domain_type', $domainType);

        // Try to resolve domain and organization
        $domain = $this->resolveDomain($hostname);

        if ($domain && $domain->is_active) {
            // Set domain and organization in the request
            $request->attributes->set('domain', $domain);
            $request->attributes->set('organization', $domain->organization);

            // Set organization context for the application
            app()->instance('current.domain', $domain);
            app()->instance('current.organization', $domain->organization);

            // Add domain configuration to view data
            if ($domain->organization) {
                view()->share('currentDomain', $domain);
                view()->share('currentOrganization', $domain->organization);
                view()->share('brandConfig', $this->getBrandConfig($domain));
            }

            // Set a customer domain flag if applicable
            if ($domainType === 'customer') {
                $request->attributes->set('is_customer_domain', true);
            }
        } elseif ($domainType === 'admin') {
            // For admin domains without a specific domain configuration
            $request->attributes->set('is_admin_domain', true);
        }

        // Ensure the current.organization is always bound, even if null
        if (! app()->bound('current.organization')) {
            app()->instance('current.organization', null);
        }

        return $next($request);
    }

    /**
     * Check if domain resolution should be skipped.
     */
    protected function shouldSkipResolution(Request $request): bool
    {
        $path = $request->path();

        // Skip for system routes
        $systemRoutes = [
            '_debugbar',
            'telescope',
            'horizon',
            'health',
            'up',
        ];

        return array_any($systemRoutes, fn ($route) => str_starts_with($path, $route));
    }

    /**
     * Determine a domain type based on the hostname.
     */
    protected function determineDomainType(string $hostname): string
    {
        $adminDomains = [
            'admin.autopay.vn',
            'app.autopay.vn',
            'dashboard.autopay.vn',
            'localhost:8000', // for local development
            '127.0.0.1:8000', // for local development
        ];

        if (in_array($hostname, $adminDomains, true)) {
            return 'admin';
        }

        return 'customer';
    }

    /**
     * Resolve domain from the hostname with caching.
     */
    protected function resolveDomain(string $hostname): ?Domain
    {
        $cacheKey = "domain.resolution.$hostname";

        return Cache::remember($cacheKey, 300, static function () use ($hostname) {
            return Domain::with('organization')
                ->where('backend_hostname', $hostname)
                ->where('is_active', true)
                ->where('status', 'active')
                ->first();
        });
    }

    /**
     * Get brand configuration for the domain.
     */
    protected function getBrandConfig(Domain $domain): array
    {
        $brandingData = $domain->data['branding'] ?? [];
        $themeData = $domain->data['theme'] ?? [];
        $seoData = $domain->data['seo'] ?? [];
        $contactData = $domain->data['contact'] ?? [];

        return [
            'brand_name' => $brandingData['name'] ?? $domain->organization->name,
            'slogan' => $brandingData['slogan'] ?? null,
            'logo_url' => $brandingData['logo_url'] ?? null,
            'favicon_url' => $brandingData['favicon_url'] ?? null,
            'theme_colors' => $themeData['colors'] ?? [
                'primary' => '#3b82f6',
                'secondary' => '#64748b',
                'accent' => '#f59e0b',
                'background' => '#ffffff',
                'foreground' => '#0f172a',
            ],
            'custom_css' => $themeData['custom_css'] ?? [],
            'meta' => [
                'title' => $seoData['title'] ?? $brandingData['name'] ?? $domain->organization->name,
                'description' => $seoData['description'] ?? null,
                'keywords' => $seoData['keywords'] ?? null,
                'og_image' => $seoData['og_image'] ?? null,
            ],
            'contact_info' => $contactData,
        ];
    }

    /**
     * Clear domain resolution cache.
     */
    public static function clearCache(string $hostname): void
    {
        Cache::forget("domain.resolution.$hostname");
    }

    /**
     * Clear all domain resolution caches.
     */
    public static function clearAllCache(): void
    {
        $backendHostnames = Domain::whereNotNull('backend_hostname')->pluck('backend_hostname');

        foreach ($backendHostnames as $hostname) {
            static::clearCache($hostname);
        }
    }
}
